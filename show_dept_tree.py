#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
展示生成的部门层级结构
"""

import re

def parse_sql_and_show_tree(sql_file_path):
    """
    解析SQL文件并展示部门树形结构
    
    Args:
        sql_file_path (str): SQL文件路径
    """
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按行分割SQL语句
        lines = content.strip().split('\n')
        
        # 解析部门信息
        departments = {}
        root_id = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 提取部门信息
            # INSERT INTO `sys_dept` (...) VALUES ('id', 'pid', 'name', 'code', ...)
            match = re.search(r"VALUES \('([^']+)', (NULL|'[^']+'), '([^']+)', '([^']+)'", line)
            if match:
                dept_id = match.group(1)
                parent_id = match.group(2)
                if parent_id == 'NULL':
                    parent_id = None
                    root_id = dept_id
                else:
                    parent_id = parent_id.strip("'")
                name = match.group(3)
                code = match.group(4)
                
                # 提取grade
                grade_match = re.search(r", (\d+), NULL, NULL\);$", line)
                grade = int(grade_match.group(1)) if grade_match else 0
                
                departments[dept_id] = {
                    'name': name,
                    'code': code,
                    'parent_id': parent_id,
                    'grade': grade,
                    'children': []
                }
        
        # 构建树形结构
        for dept_id, dept in departments.items():
            if dept['parent_id'] and dept['parent_id'] in departments:
                departments[dept['parent_id']]['children'].append(dept_id)
        
        # 递归打印树形结构
        def print_tree(dept_id, level=0):
            if dept_id not in departments:
                return
            
            dept = departments[dept_id]
            indent = "  " * level
            print(f"{indent}├─ {dept['name']} ({dept['code']}) [Grade {dept['grade']}]")
            
            # 打印子部门
            for child_id in dept['children']:
                print_tree(child_id, level + 1)
        
        print("🏢 部门层级结构:")
        print("=" * 50)
        if root_id:
            print_tree(root_id)
        
        print(f"\n📊 统计信息:")
        print(f"总部门数: {len(departments)}")
        
        # 按级别统计
        grade_counts = {}
        for dept in departments.values():
            grade = dept['grade']
            grade_counts[grade] = grade_counts.get(grade, 0) + 1
        
        for grade in sorted(grade_counts.keys()):
            print(f"{grade}级部门: {grade_counts[grade]} 个")
            
    except Exception as e:
        print(f"❌ 解析过程中发生错误: {e}")

if __name__ == '__main__':
    parse_sql_and_show_tree('insert_data.sql')
