#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试生成的SQL文件语法是否正确
"""

import re

def validate_sql_file(sql_file_path):
    """
    验证SQL文件的基本语法
    
    Args:
        sql_file_path (str): SQL文件路径
    
    Returns:
        bool: 验证是否通过
    """
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按行分割SQL语句
        lines = content.strip().split('\n')
        
        print(f"总共生成了 {len(lines)} 条SQL语句")
        
        # 验证每条SQL语句的基本格式
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            # 检查是否以INSERT开头
            if not line.startswith('INSERT INTO'):
                print(f"第 {i} 行格式错误: 不是INSERT语句")
                return False
            
            # 检查是否以分号结尾
            if not line.endswith(';'):
                print(f"第 {i} 行格式错误: 缺少分号")
                return False
            
            # 检查VALUES关键字
            if 'VALUES' not in line:
                print(f"第 {i} 行格式错误: 缺少VALUES关键字")
                return False
        
        print("✅ SQL文件语法验证通过！")
        
        # 统计各级部门数量
        grade_counts = {}
        for line in lines:
            # 提取grade值 - 查找最后一个数字（grade值）
            match = re.search(r", (\d+), NULL, NULL\);$", line)
            if match:
                grade = int(match.group(1))
                grade_counts[grade] = grade_counts.get(grade, 0) + 1
        
        print("\n📊 各级部门统计:")
        for grade in sorted(grade_counts.keys()):
            print(f"  {grade}级部门: {grade_counts[grade]} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    validate_sql_file('insert_data.sql')
