# -*- coding: utf-8 -*-
"""
这是一个Python脚本，用于将新的Excel格式（包含 full_path, name, grade, parent_path）
的数据导入到MySQL数据库中。
脚本将读取一个名为 'org_data.xlsx' 的Excel文件，处理各列数据，
并根据提供的规则生成 `id`, `pid`, `name`, `code` 等字段，
最终生成一个包含所有INSERT语句的SQL文件。

使用方法:
1. 确保您已安装 pandas 和 openpyxl 库。如果未安装，请在终端中运行:
   pip install pandas openpyxl
2. 将您的Excel文件命名为 'org_data.xlsx'，并确保包含 'full_path', 'name', 'grade', 'parent_path' 列。
   这些列可以没有表头，但必须是固定的第1, 2, 3, 4列。
3. 将此脚本与您的Excel文件放在同一目录下。
4. 在终端中运行此脚本:
   python your_script_name.py
5. 脚本运行完成后，会生成一个 'insert_data.sql' 文件。您可以使用MySQL客户端执行此文件。
"""

import pandas as pd
import uuid
from datetime import datetime
import os


def generate_insert_script(excel_file_path, table_name='sys_dept'):
    """
    读取Excel文件并生成SQL INSERT脚本。

    Args:
        excel_file_path (str): Excel文件的路径。
        table_name (str): 目标表的名称。

    Returns:
        str: 包含所有INSERT语句的字符串。
    """
    try:
        # 读取Excel文件，使用第一行作为表头
        df = pd.read_excel(excel_file_path, header=0)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_file_path}")
        return ""
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")
        return ""

    # 用于存储所有部门的映射关系: {full_path: {'id': ..., 'code': ...}}
    dept_map = {}
    # 用于存储每个父部门下的子部门序号: {父部门code: 序号}
    child_counter = {}
    # 存储所有生成的SQL语句
    sql_statements = []

    # 预先定义根节点的ID和Code
    root_name = '中国电建'
    root_code = 'DJDC'
    root_full_path = '中国电建'
    root_id = str(uuid.uuid4())
    dept_map[root_full_path] = {'id': root_id, 'code': root_code}
    child_counter[root_code] = 0

    # 插入根节点的SQL语句
    root_sql = (
        f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, `type`, `source`, `status`, `sort`, `createTime`, `createPeople`, `bpmStatus`, `orgType`, `orderNo`, `grade`, `updateTime`, `updatePeople`) VALUES "
        f"('{root_id}', NULL, '{root_name}', '{root_code}', NULL, NULL, NULL, 0, 1, '{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}', '系统导入', NULL, NULL, NULL, 1, NULL, NULL);"
    )
    sql_statements.append(root_sql)

    # 遍历Excel中的每一行数据，从第二行开始（假设第一行是根节点）
    for index, row in df.iterrows():
        full_path = str(row['full_path']).strip()
        current_name = str(row['name']).strip()
        grade = int(row['grade'])
        parent_path = str(row['parent_path']).strip()

        # 检查当前部门是否已处理过
        if full_path in dept_map:
            continue

        # 确定父部门的信息
        parent_info = dept_map.get(parent_path, {})
        parent_id = parent_info.get('id')
        parent_code = parent_info.get('code')

        if not parent_id or not parent_code:
            print(f"警告: 找不到父部门 '{parent_path}'，跳过 '{full_path}'")
            continue

        # 生成新的ID
        current_id = str(uuid.uuid4())

        # 生成部门Code
        if parent_code not in child_counter:
            child_counter[parent_code] = 1
        else:
            child_counter[parent_code] += 1

        # 将序号格式化为两位数
        child_num = str(child_counter[parent_code]).zfill(2)

        # 拼接新的部门Code
        current_code = f"{parent_code}{child_num}"

        # 插入新部门的映射关系
        dept_map[full_path] = {'id': current_id, 'code': current_code}

        # 获取当前时间
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 拼接SQL INSERT语句 - pid应该是父部门的id
        sql = (
            f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, `type`, `source`, `status`, `sort`, `createTime`, `createPeople`, `bpmStatus`, `orgType`, `orderNo`, `grade`, `updateTime`, `updatePeople`) VALUES "
            f"('{current_id}', '{parent_id}', '{current_name}', '{current_code}', NULL, NULL, NULL, 0, 1, '{now}', '系统导入', NULL, NULL, NULL, {grade}, NULL, NULL);"
        )
        sql_statements.append(sql)

    return "\n".join(sql_statements)


if __name__ == '__main__':
    excel_file = 'organizations.xlsx'
    sql_content = generate_insert_script(excel_file)

    if sql_content:
        # 写入SQL文件
        output_file = 'insert_data.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        print(f"成功生成SQL脚本文件: {output_file}")
        print("您现在可以使用此文件将数据导入到MySQL中。")
    else:
        print("未能生成SQL内容，请检查Excel文件是否存在且格式正确。")
