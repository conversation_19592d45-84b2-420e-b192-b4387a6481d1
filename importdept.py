# -*- coding: utf-8 -*-
"""
这是一个Python脚本，用于将新的Excel格式（包含 full_path, name, grade, parent_path）
的数据导入到MySQL数据库中。
脚本将读取一个名为 'org_data.xlsx' 的Excel文件，处理各列数据，
并根据提供的规则生成 `id`, `pid`, `name`, `code` 等字段，
最终生成一个包含所有INSERT语句的SQL文件。

使用方法:
1. 确保您已安装 pandas 和 openpyxl 库。如果未安装，请在终端中运行:
   pip install pandas openpyxl
2. 将您的Excel文件命名为 'org_data.xlsx'，并确保包含 'full_path', 'name', 'grade', 'parent_path' 列。
   这些列可以没有表头，但必须是固定的第1, 2, 3, 4列。
3. 将此脚本与您的Excel文件放在同一目录下。
4. 在终端中运行此脚本:
   python your_script_name.py
5. 脚本运行完成后，会生成一个 'insert_data.sql' 文件。您可以使用MySQL客户端执行此文件。
"""

import pandas as pd
import uuid
from datetime import datetime
import os


def create_missing_parent(parent_path, dept_map, child_counter, sql_statements, table_name):
    """
    递归创建缺失的父部门

    Args:
        parent_path (str): 父部门路径
        dept_map (dict): 部门映射字典
        child_counter (dict): 子部门计数器
        sql_statements (list): SQL语句列表
        table_name (str): 表名

    Returns:
        bool: 是否成功创建
    """
    if parent_path in dept_map:
        return True

    # 解析父部门路径，找到它的父部门
    path_parts = parent_path.split('>')
    if len(path_parts) <= 1:
        return False

    # 构造父部门的父部门路径
    grandparent_path = '>'.join(path_parts[:-1])
    current_name = path_parts[-1].strip()

    # 递归确保祖父部门存在
    if not create_missing_parent(grandparent_path, dept_map, child_counter, sql_statements, table_name):
        return False

    # 现在创建当前父部门
    grandparent_info = dept_map.get(grandparent_path, {})
    grandparent_code = grandparent_info.get('code')

    if not grandparent_code:
        return False

    # 生成新的ID和Code
    current_id = str(uuid.uuid4())

    if grandparent_code not in child_counter:
        child_counter[grandparent_code] = 1
    else:
        child_counter[grandparent_code] += 1

    child_num = str(child_counter[grandparent_code]).zfill(2)
    current_code = f"{grandparent_code}{child_num}"

    # 推断grade（比祖父部门高一级）
    grade = grandparent_info.get('grade', 1) + 1

    # 插入新部门的映射关系
    dept_map[parent_path] = {'id': current_id, 'code': current_code, 'grade': grade}

    # 生成SQL
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    sql = (
        f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, `type`, `source`, `status`, `sort`, `createTime`, `createPeople`, `bpmStatus`, `orgType`, `orderNo`, `grade`, `updateTime`, `updatePeople`) VALUES "
        f"('{current_id}', '{grandparent_code}', '{current_name}', '{current_code}', NULL, NULL, NULL, 0, 1, '{now}', '系统导入', NULL, NULL, NULL, {grade}, NULL, NULL);"
    )
    sql_statements.append(sql)

    print(f"自动创建缺失的父部门: {parent_path} (code: {current_code})")
    return True

def generate_insert_script(excel_file_path, table_name='sys_dept'):
    """
    读取Excel文件并生成SQL INSERT脚本。

    Args:
        excel_file_path (str): Excel文件的路径。
        table_name (str): 目标表的名称。

    Returns:
        str: 包含所有INSERT语句的字符串。
    """
    try:
        # 读取Excel文件，使用第一行作为表头
        df = pd.read_excel(excel_file_path, header=0)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_file_path}")
        return ""
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")
        return ""

    # 用于存储所有部门的映射关系: {full_path: {'id': ..., 'code': ...}}
    dept_map = {}
    # 用于存储每个父部门下的子部门序号: {父部门code: 序号}
    child_counter = {}
    # 存储所有生成的SQL语句
    sql_statements = []

    # 预先定义根节点的ID和Code
    root_name = '中国电建'
    root_code = 'DJDC'
    root_full_path = '中国电建'
    root_id = str(uuid.uuid4())
    dept_map[root_full_path] = {'id': root_id, 'code': root_code, 'grade': 1}
    child_counter[root_code] = 0

    # 插入根节点的SQL语句
    root_sql = (
        f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, `type`, `source`, `status`, `sort`, `createTime`, `createPeople`, `bpmStatus`, `orgType`, `orderNo`, `grade`, `updateTime`, `updatePeople`) VALUES "
        f"('{root_id}', NULL, '{root_name}', '{root_code}', NULL, NULL, NULL, 0, 1, '{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}', '系统导入', NULL, NULL, NULL, 1, NULL, NULL);"
    )
    sql_statements.append(root_sql)

    # 按grade排序，确保父部门先于子部门处理
    df_sorted = df.sort_values('grade')

    # 使用多轮处理，直到所有能处理的数据都被处理完
    processed_count = 0
    max_rounds = 10  # 最多处理10轮，避免无限循环

    for round_num in range(max_rounds):
        round_processed = 0
        print(f"第 {round_num + 1} 轮处理...")

        for index, row in df_sorted.iterrows():
            full_path = str(row['full_path']).strip()
            current_name = str(row['name']).strip()
            grade = int(row['grade'])
            parent_path = str(row['parent_path']).strip()

            # 处理NaN值
            if parent_path == 'nan':
                parent_path = None

            # 检查当前部门是否已处理过
            if full_path in dept_map:
                continue

            # 如果是根节点（没有父部门），跳过（已经预先插入）
            if parent_path is None or parent_path == 'nan':
                continue

            # 确定父部门的信息，如果不存在则自动创建
            parent_info = dept_map.get(parent_path, {})
            if not parent_info:
                # 尝试自动创建缺失的父部门
                if not create_missing_parent(parent_path, dept_map, child_counter, sql_statements, table_name):
                    # 无法创建父部门，这轮跳过
                    continue
                parent_info = dept_map.get(parent_path, {})

            parent_id = parent_info.get('id')
            parent_code = parent_info.get('code')

            if not parent_id or not parent_code:
                # 这轮找不到父部门，下轮再试
                continue

            # 生成新的ID
            current_id = str(uuid.uuid4())

            # 生成部门Code
            if parent_code not in child_counter:
                child_counter[parent_code] = 1
            else:
                child_counter[parent_code] += 1

            # 将序号格式化为两位数
            child_num = str(child_counter[parent_code]).zfill(2)

            # 拼接新的部门Code
            current_code = f"{parent_code}{child_num}"

            # 插入新部门的映射关系
            dept_map[full_path] = {'id': current_id, 'code': current_code, 'grade': grade}

            # 获取当前时间
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 拼接SQL INSERT语句 - pid应该是父部门的code
            sql = (
                f"INSERT INTO `{table_name}` (`id`, `pid`, `name`, `code`, `leader`, `type`, `source`, `status`, `sort`, `createTime`, `createPeople`, `bpmStatus`, `orgType`, `orderNo`, `grade`, `updateTime`, `updatePeople`) VALUES "
                f"('{current_id}', '{parent_code}', '{current_name}', '{current_code}', NULL, NULL, NULL, 0, 1, '{now}', '系统导入', NULL, NULL, NULL, {grade}, NULL, NULL);"
            )
            sql_statements.append(sql)
            round_processed += 1
            processed_count += 1

        print(f"第 {round_num + 1} 轮处理了 {round_processed} 条记录")

        # 如果这轮没有处理任何记录，说明剩余的都无法找到父部门，退出循环
        if round_processed == 0:
            break

    print(f"总共处理了 {processed_count} 条记录")

    # 统计未处理的记录
    unprocessed = []
    for index, row in df.iterrows():
        full_path = str(row['full_path']).strip()
        if full_path not in dept_map and str(row['parent_path']).strip() != 'nan':
            unprocessed.append(full_path)

    if unprocessed:
        print(f"警告: 有 {len(unprocessed)} 条记录未能处理（找不到父部门）:")
        for i, path in enumerate(unprocessed[:10]):  # 只显示前10个
            print(f"  {i+1}. {path}")
        if len(unprocessed) > 10:
            print(f"  ... 还有 {len(unprocessed) - 10} 条记录未显示")

    return "\n".join(sql_statements)


if __name__ == '__main__':
    excel_file = 'organizations.xlsx'
    sql_content = generate_insert_script(excel_file)

    if sql_content:
        # 写入SQL文件
        output_file = 'insert_data.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        print(f"成功生成SQL脚本文件: {output_file}")
        print("您现在可以使用此文件将数据导入到MySQL中。")
    else:
        print("未能生成SQL内容，请检查Excel文件是否存在且格式正确。")
